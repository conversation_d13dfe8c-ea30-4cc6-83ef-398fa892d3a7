/**
 * Test utility for blur detection - to be used in development/testing
 * This helps us verify that OpenCV.js works in the actual React Native environment
 */

import { BlurDetectionService } from '~/services/BlurDetectionService';

/**
 * Test OpenCV.js initialization in the real app environment
 */
export const testOpenCVInitialization = async (): Promise<{
  success: boolean;
  message: string;
  buildInfo?: string;
}> => {
  try {
    console.log('🔍 Testing OpenCV.js initialization...');
    
    const result = await BlurDetectionService.testOpenCV();
    
    if (result.success) {
      console.log('✅ OpenCV.js initialized successfully!');
      console.log('Build info:', result.buildInfo);
      return {
        success: true,
        message: 'OpenCV.js initialized successfully',
        buildInfo: result.buildInfo,
      };
    } else {
      console.log('❌ OpenCV.js initialization failed:', result.error);
      return {
        success: false,
        message: `OpenCV.js initialization failed: ${result.error}`,
      };
    }
  } catch (error) {
    console.log('❌ Error testing OpenCV.js:', error);
    return {
      success: false,
      message: `Error testing OpenCV.js: ${error}`,
    };
  }
};

/**
 * Create a simple test image (1x1 red pixel) for testing
 */
export const createTestImageData = (): ImageData => {
  // Create a simple 1x1 red pixel for testing
  const data = new Uint8ClampedArray([255, 0, 0, 255]); // Red pixel
  return {
    data,
    width: 1,
    height: 1,
  } as ImageData;
};

/**
 * Create a test image with more pixels for better blur detection testing
 */
export const createLargerTestImageData = (size: number = 10): ImageData => {
  const data = new Uint8ClampedArray(size * size * 4);
  
  // Create a simple pattern - alternating black and white pixels
  for (let i = 0; i < size * size; i++) {
    const isWhite = (Math.floor(i / size) + (i % size)) % 2 === 0;
    const baseIndex = i * 4;
    
    if (isWhite) {
      data[baseIndex] = 255;     // R
      data[baseIndex + 1] = 255; // G
      data[baseIndex + 2] = 255; // B
      data[baseIndex + 3] = 255; // A
    } else {
      data[baseIndex] = 0;       // R
      data[baseIndex + 1] = 0;   // G
      data[baseIndex + 2] = 0;   // B
      data[baseIndex + 3] = 255; // A
    }
  }
  
  return {
    data,
    width: size,
    height: size,
  } as ImageData;
};

/**
 * Test blur detection with a simple image
 */
export const testBlurDetectionWithSimpleImage = async (): Promise<{
  success: boolean;
  message: string;
  result?: any;
}> => {
  try {
    console.log('🔍 Testing blur detection with simple image...');
    
    const testImage = createLargerTestImageData(10);
    const result = await BlurDetectionService.calculateLaplacianVariance(testImage);
    
    console.log('Blur detection result:', result);
    
    if (result.error) {
      return {
        success: false,
        message: `Blur detection failed: ${result.error}`,
      };
    }
    
    return {
      success: true,
      message: `Blur detection successful. Variance: ${result.laplacianVariance}, Is Blurry: ${result.isBlurry}`,
      result,
    };
  } catch (error) {
    console.log('❌ Error testing blur detection:', error);
    return {
      success: false,
      message: `Error testing blur detection: ${error}`,
    };
  }
};

/**
 * Run all blur detection tests
 */
export const runAllBlurDetectionTests = async (): Promise<{
  initializationTest: any;
  blurDetectionTest: any;
}> => {
  console.log('🚀 Running all blur detection tests...');
  
  const initializationTest = await testOpenCVInitialization();
  const blurDetectionTest = await testBlurDetectionWithSimpleImage();
  
  console.log('📊 Test Results:');
  console.log('- Initialization:', initializationTest.success ? '✅' : '❌');
  console.log('- Blur Detection:', blurDetectionTest.success ? '✅' : '❌');
  
  return {
    initializationTest,
    blurDetectionTest,
  };
};
