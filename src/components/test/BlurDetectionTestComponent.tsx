/**
 * Test component for blur detection - for development/testing only
 * This component helps us verify that OpenCV.js works in the actual React Native environment
 */

import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import FilledButton from '~/components/buttons/FilledButton';
import { runAllBlurDetectionTests } from '~/utils/testBlurDetection';
import colors from '~/styles/colors';
import { h4, h5 } from '~/styles/text';

interface TestResult {
  initializationTest: {
    success: boolean;
    message: string;
    buildInfo?: string;
  };
  blurDetectionTest: {
    success: boolean;
    message: string;
    result?: any;
  };
}

const BlurDetectionTestComponent: React.FC = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult | null>(null);

  const runTests = async () => {
    setIsRunning(true);
    setTestResults(null);
    
    try {
      const results = await runAllBlurDetectionTests();
      setTestResults(results);
    } catch (error) {
      console.error('Error running tests:', error);
      setTestResults({
        initializationTest: {
          success: false,
          message: `Test execution failed: ${error}`,
        },
        blurDetectionTest: {
          success: false,
          message: 'Test not run due to previous error',
        },
      });
    } finally {
      setIsRunning(false);
    }
  };

  const renderTestResult = (title: string, result: { success: boolean; message: string; buildInfo?: string; result?: any }) => (
    <View style={styles.testResult}>
      <Text style={[h5, { color: result.success ? colors.green600 : colors.error }]}>
        {result.success ? '✅' : '❌'} {title}
      </Text>
      <Text style={styles.message}>{result.message}</Text>
      {result.buildInfo && (
        <Text style={styles.details}>Build Info: {result.buildInfo}</Text>
      )}
      {result.result && (
        <Text style={styles.details}>
          Variance: {result.result.laplacianVariance}, Is Blurry: {result.result.isBlurry ? 'Yes' : 'No'}
        </Text>
      )}
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        <Text style={[h4, styles.title]}>Blur Detection Test</Text>
        <Text style={styles.description}>
          This component tests OpenCV.js initialization and blur detection functionality in the React Native environment.
        </Text>

        <FilledButton
          title={isRunning ? 'Running Tests...' : 'Run Blur Detection Tests'}
          onPress={runTests}
          disabled={isRunning}
          style={styles.button}
        />

        {testResults && (
          <View style={styles.results}>
            <Text style={[h5, styles.resultsTitle]}>Test Results:</Text>
            {renderTestResult('OpenCV.js Initialization', testResults.initializationTest)}
            {renderTestResult('Blur Detection', testResults.blurDetectionTest)}
          </View>
        )}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.backgroundWhite,
  },
  content: {
    padding: 20,
  },
  title: {
    marginBottom: 10,
    textAlign: 'center',
  },
  description: {
    marginBottom: 20,
    textAlign: 'center',
    color: colors.grey600,
  },
  button: {
    marginBottom: 20,
  },
  results: {
    marginTop: 10,
  },
  resultsTitle: {
    marginBottom: 15,
  },
  testResult: {
    backgroundColor: colors.white,
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    borderWidth: 1,
    borderColor: colors.lightGray,
  },
  message: {
    marginTop: 5,
    color: colors.grey600,
  },
  details: {
    marginTop: 5,
    fontSize: 12,
    color: colors.grey600,
    fontFamily: 'monospace',
  },
});

export default BlurDetectionTestComponent;
