// Mock native modules that cause issues in tests
jest.mock('react-native-background-geolocation', () => ({}));
jest.mock('react-native-background-fetch', () => ({}));
jest.mock('react-native-geolocation-service', () => ({}));
jest.mock('react-native-device-info', () => ({}));

// Mock OpenCV.js
const mockOpenCV = {
  getBuildInformation: jest.fn(() => 'Mock OpenCV Build Info'),
  matFromImageData: jest.fn(),
  cvtColor: jest.fn(),
  Laplacian: jest.fn(),
  meanStdDev: jest.fn(),
  COLOR_RGBA2GRAY: 6,
  COLOR_RGB2GRAY: 7,
  CV_64F: 6,
  Mat: jest.fn().mockImplementation(() => ({
    channels: jest.fn(() => 4),
    copyTo: jest.fn(),
    delete: jest.fn(),
    data64F: [0, 0, 0, 100], // Mock stddev value for variance calculation
  })),
};

// Mock the default export as a resolved promise
jest.mock('@techstark/opencv-js', () => Promise.resolve(mockOpenCV));

import { BlurDetectionService } from '~/services/BlurDetectionService';

// Type definition for ImageData (for testing)
interface ImageData {
  data: Uint8ClampedArray;
  width: number;
  height: number;
}

describe('BlurDetectionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the service state
    (BlurDetectionService as any).isInitialized = false;
    (BlurDetectionService as any).cv = null;
    (BlurDetectionService as any).initializationPromise = null;
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('Basic Service Structure', () => {
    it('should be defined', () => {
      expect(BlurDetectionService).toBeDefined();
    });

    it('should have required methods', () => {
      expect(typeof BlurDetectionService.testOpenCV).toBe('function');
      expect(typeof BlurDetectionService.calculateLaplacianVariance).toBe('function');
      expect(typeof BlurDetectionService.isReady).toBe('function');
      expect(typeof BlurDetectionService.getOpenCV).toBe('function');
    });
  });

  describe('isReady', () => {
    it('should return false initially', () => {
      expect(BlurDetectionService.isReady()).toBe(false);
    });
  });

  describe('Error Handling', () => {
    const mockImageData = {
      data: new Uint8ClampedArray([255, 0, 0, 255]), // Red pixel
      width: 1,
      height: 1,
    } as ImageData;

    it('should handle OpenCV initialization failure gracefully', async () => {
      const result = await BlurDetectionService.testOpenCV();

      // Since OpenCV isn't properly initialized in test environment,
      // we expect it to fail gracefully
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle calculateLaplacianVariance errors gracefully', async () => {
      const result = await BlurDetectionService.calculateLaplacianVariance(mockImageData);

      // Should return error state when OpenCV isn't available
      expect(result.laplacianVariance).toBe(0);
      expect(result.isBlurry).toBe(true);
      expect(result.error).toBeDefined();
    });
  });
});
