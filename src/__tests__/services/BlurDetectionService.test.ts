// Mock native modules that cause issues in tests
jest.mock('react-native-background-geolocation', () => ({}));
jest.mock('react-native-background-fetch', () => ({}));
jest.mock('react-native-geolocation-service', () => ({}));
jest.mock('react-native-device-info', () => ({}));

// Mock OpenCV.js
const mockOpenCV = {
  getBuildInformation: jest.fn(() => 'Mock OpenCV Build Info'),
  matFromImageData: jest.fn(),
  cvtColor: jest.fn(),
  Laplacian: jest.fn(),
  meanStdDev: jest.fn(),
  COLOR_RGBA2GRAY: 6,
  COLOR_RGB2GRAY: 7,
  CV_64F: 6,
  Mat: jest.fn().mockImplementation(() => ({
    channels: jest.fn(() => 4),
    copyTo: jest.fn(),
    delete: jest.fn(),
    data64F: [0, 0, 0, 100], // Mock stddev value for variance calculation
  })),
};

jest.mock('@techstark/opencv-js', () => Promise.resolve(mockOpenCV));

import { BlurDetectionService } from '~/services/BlurDetectionService';

describe('BlurDetectionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the service state
    (BlurDetectionService as any).isInitialized = false;
    (BlurDetectionService as any).cv = null;
    (BlurDetectionService as any).initializationPromise = null;
  });

  afterEach(() => {
    jest.clearAllTimers();
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe('testOpenCV', () => {
    it('should initialize OpenCV and return build information', async () => {
      const result = await BlurDetectionService.testOpenCV();

      expect(result.success).toBe(true);
      expect(result.buildInfo).toBe('Mock OpenCV Build Info');
      expect(result.error).toBeUndefined();
      expect(mockOpenCV.getBuildInformation).toHaveBeenCalled();
    });

    it('should handle initialization errors', async () => {
      // Mock OpenCV to throw an error
      const originalGetBuildInfo = mockOpenCV.getBuildInformation;
      mockOpenCV.getBuildInformation = jest.fn(() => {
        throw new Error('Mock initialization error');
      });

      const result = await BlurDetectionService.testOpenCV();

      expect(result.success).toBe(false);
      expect(result.error).toContain('Mock initialization error');
      expect(result.buildInfo).toBeUndefined();

      // Restore original mock
      mockOpenCV.getBuildInformation = originalGetBuildInfo;
    });
  });

  describe('isReady', () => {
    it('should return false initially', () => {
      expect(BlurDetectionService.isReady()).toBe(false);
    });

    it('should return true after successful initialization', async () => {
      await BlurDetectionService.testOpenCV();
      expect(BlurDetectionService.isReady()).toBe(true);
    });
  });

  describe('calculateLaplacianVariance', () => {
    const mockImageData = {
      data: new Uint8ClampedArray([255, 0, 0, 255]), // Red pixel
      width: 1,
      height: 1,
    } as ImageData;

    beforeEach(() => {
      // Set up mock Mat behavior
      const mockMat = {
        channels: jest.fn(() => 4),
        copyTo: jest.fn(),
        delete: jest.fn(),
        data64F: [0, 0, 0, 10], // Mock stddev value = 10, so variance = 100
      };

      mockOpenCV.Mat.mockImplementation(() => mockMat);
      mockOpenCV.matFromImageData.mockReturnValue(mockMat);
    });

    it('should calculate Laplacian variance for RGBA image', async () => {
      const result = await BlurDetectionService.calculateLaplacianVariance(mockImageData);

      expect(result.laplacianVariance).toBe(100); // 10^2 = 100
      expect(result.isBlurry).toBe(false); // 100 >= default threshold of 100
      expect(result.error).toBeUndefined();

      expect(mockOpenCV.matFromImageData).toHaveBeenCalledWith(mockImageData);
      expect(mockOpenCV.cvtColor).toHaveBeenCalled();
      expect(mockOpenCV.Laplacian).toHaveBeenCalled();
      expect(mockOpenCV.meanStdDev).toHaveBeenCalled();
    });

    it('should detect blurry image with low variance', async () => {
      // Mock low stddev value
      const mockMat = {
        channels: jest.fn(() => 4),
        copyTo: jest.fn(),
        delete: jest.fn(),
        data64F: [0, 0, 0, 5], // stddev = 5, variance = 25
      };
      mockOpenCV.Mat.mockImplementation(() => mockMat);

      const result = await BlurDetectionService.calculateLaplacianVariance(mockImageData);

      expect(result.laplacianVariance).toBe(25);
      expect(result.isBlurry).toBe(true); // 25 < default threshold of 100
    });

    it('should use custom blur threshold', async () => {
      const result = await BlurDetectionService.calculateLaplacianVariance(
        mockImageData,
        { blurThreshold: 50 }
      );

      expect(result.laplacianVariance).toBe(100);
      expect(result.isBlurry).toBe(false); // 100 >= custom threshold of 50
    });

    it('should handle errors gracefully', async () => {
      mockOpenCV.matFromImageData.mockImplementation(() => {
        throw new Error('Mock processing error');
      });

      const result = await BlurDetectionService.calculateLaplacianVariance(mockImageData);

      expect(result.laplacianVariance).toBe(0);
      expect(result.isBlurry).toBe(true);
      expect(result.error).toContain('Mock processing error');
    });
  });

  describe('getOpenCV', () => {
    it('should return OpenCV instance after initialization', async () => {
      const cv = await BlurDetectionService.getOpenCV();
      expect(cv).toBe(mockOpenCV);
    });
  });
});
