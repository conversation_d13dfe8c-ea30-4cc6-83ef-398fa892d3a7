/**
 * BlurDetectionService handles image blur detection using OpenCV.js
 * Replicates Python cv2.Laplacian functionality for blur detection
 */

import cvReadyPromise from '@techstark/opencv-js';

// Type definition for ImageData (available in browser environments)
interface ImageData {
  data: Uint8ClampedArray;
  width: number;
  height: number;
}

export interface BlurDetectionResult {
  laplacianVariance: number;
  isBlurry: boolean;
  error?: string;
}

export interface BlurDetectionOptions {
  blurThreshold?: number; // Default threshold for determining if image is blurry
}

class BlurDetectionServiceClass {
  private cv: any = null;
  private isInitialized = false;
  private initializationPromise: Promise<void> | null = null;

  /**
   * Initialize OpenCV.js
   */
  private async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._doInitialize();
    return this.initializationPromise;
  }

  private async _doInitialize(): Promise<void> {
    try {
      console.log('BlurDetectionService: Initializing OpenCV.js...');
      this.cv = await cvReadyPromise;

      if (!this.cv) {
        throw new Error('OpenCV.js failed to load');
      }

      this.isInitialized = true;
      console.log('BlurDetectionService: OpenCV.js initialized successfully');

      // Only try to get build info if the method exists (for testing compatibility)
      if (this.cv.getBuildInformation) {
        console.log('OpenCV.js build info:', this.cv.getBuildInformation());
      }
    } catch (error) {
      console.error('BlurDetectionService: Failed to initialize OpenCV.js:', error);
      throw new Error(`Failed to initialize OpenCV.js: ${error}`);
    }
  }

  /**
   * Calculate Laplacian variance for blur detection
   * Replicates: cv2.Laplacian(image, cv2.CV_64F).var()
   */
  async calculateLaplacianVariance(
    imageData: ImageData,
    options: BlurDetectionOptions = {}
  ): Promise<BlurDetectionResult> {
    try {
      await this.initialize();

      if (!this.cv) {
        throw new Error('OpenCV.js not initialized');
      }

      const { blurThreshold = 100 } = options;

      // Create OpenCV Mat from ImageData
      const src = this.cv.matFromImageData(imageData);
      
      // Convert to grayscale if needed (ImageData is typically RGBA)
      const gray = new this.cv.Mat();
      if (src.channels() === 4) {
        // RGBA to grayscale
        this.cv.cvtColor(src, gray, this.cv.COLOR_RGBA2GRAY);
      } else if (src.channels() === 3) {
        // RGB to grayscale
        this.cv.cvtColor(src, gray, this.cv.COLOR_RGB2GRAY);
      } else {
        // Already grayscale
        src.copyTo(gray);
      }

      // Apply Laplacian operator (equivalent to cv2.Laplacian(image, cv2.CV_64F))
      const laplacian = new this.cv.Mat();
      this.cv.Laplacian(gray, laplacian, this.cv.CV_64F);

      // Calculate variance
      const mean = new this.cv.Mat();
      const stddev = new this.cv.Mat();
      this.cv.meanStdDev(laplacian, mean, stddev);
      
      // Variance = stddev^2
      const variance = Math.pow(stddev.data64F[0], 2);

      // Clean up memory
      src.delete();
      gray.delete();
      laplacian.delete();
      mean.delete();
      stddev.delete();

      const isBlurry = variance < blurThreshold;

      return {
        laplacianVariance: variance,
        isBlurry,
      };
    } catch (error) {
      console.error('BlurDetectionService: Error calculating Laplacian variance:', error);
      return {
        laplacianVariance: 0,
        isBlurry: true,
        error: `Failed to calculate blur: ${error}`,
      };
    }
  }

  /**
   * Test method to verify OpenCV.js is working
   */
  async testOpenCV(): Promise<{ success: boolean; buildInfo?: string; error?: string }> {
    try {
      await this.initialize();
      
      if (!this.cv) {
        throw new Error('OpenCV.js not initialized');
      }

      const buildInfo = this.cv.getBuildInformation();
      return {
        success: true,
        buildInfo,
      };
    } catch (error) {
      console.error('BlurDetectionService: OpenCV test failed:', error);
      return {
        success: false,
        error: `OpenCV test failed: ${error}`,
      };
    }
  }

  /**
   * Get OpenCV.js instance (for advanced usage)
   */
  async getOpenCV(): Promise<any> {
    await this.initialize();
    return this.cv;
  }

  /**
   * Check if service is initialized
   */
  isReady(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const BlurDetectionService = new BlurDetectionServiceClass();
