/**
 * BlurDetectionService handles image blur detection using pure JavaScript
 * Replicates Python cv2.Laplacian functionality for blur detection without OpenCV.js
 */

// Type definition for ImageData (available in browser environments)
interface ImageData {
  data: Uint8ClampedArray;
  width: number;
  height: number;
}

export interface BlurDetectionResult {
  laplacianVariance: number;
  isBlurry: boolean;
  error?: string;
}

export interface BlurDetectionOptions {
  blurThreshold?: number; // Default threshold for determining if image is blurry
}

export interface BlurTestResult {
  success: boolean;
  message?: string;
  error?: string;
}

class BlurDetectionServiceClass {
  private isInitialized = true; // Always ready since we're using pure JS

  /**
   * Convert RGBA image data to grayscale
   */
  private rgbaToGrayscale(imageData: ImageData): number[] {
    const { data, width, height } = imageData;
    const grayscale = new Array(width * height);

    for (let i = 0; i < width * height; i++) {
      const r = data[i * 4];
      const g = data[i * 4 + 1];
      const b = data[i * 4 + 2];
      // Standard grayscale conversion formula
      grayscale[i] = 0.299 * r + 0.587 * g + 0.114 * b;
    }

    return grayscale;
  }

  /**
   * Apply Laplacian kernel to detect edges (pure JavaScript implementation)
   * Replicates cv2.Laplacian functionality
   */
  private applyLaplacianKernel(grayscale: number[], width: number, height: number): number[] {
    const result = new Array(width * height).fill(0);

    // Laplacian kernel (8-connected)
    const kernel = [
      [0, -1, 0],
      [-1, 4, -1],
      [0, -1, 0]
    ];

    // Apply kernel to each pixel (excluding borders)
    for (let y = 1; y < height - 1; y++) {
      for (let x = 1; x < width - 1; x++) {
        let sum = 0;

        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const pixelIndex = (y + ky) * width + (x + kx);
            sum += grayscale[pixelIndex] * kernel[ky + 1][kx + 1];
          }
        }

        result[y * width + x] = Math.abs(sum);
      }
    }

    return result;
  }

  /**
   * Calculate variance of an array (replicates numpy.var())
   */
  private calculateVariance(values: number[]): number {
    const n = values.length;
    if (n === 0) return 0;

    // Calculate mean
    const mean = values.reduce((sum, val) => sum + val, 0) / n;

    // Calculate variance
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / n;

    return variance;
  }

  /**
   * Calculate Laplacian variance for blur detection (Pure JavaScript implementation)
   * Replicates: cv2.Laplacian(image, cv2.CV_64F).var()
   */
  async calculateLaplacianVariance(
    imageData: ImageData,
    options: BlurDetectionOptions = {}
  ): Promise<BlurDetectionResult> {
    try {
      const { blurThreshold = 100 } = options;
      const { width, height } = imageData;

      console.log(`BlurDetectionService: Processing ${width}x${height} image`);

      // Step 1: Convert RGBA to grayscale
      const grayscale = this.rgbaToGrayscale(imageData);

      // Step 2: Apply Laplacian kernel for edge detection
      const laplacianResult = this.applyLaplacianKernel(grayscale, width, height);

      // Step 3: Calculate variance of the Laplacian result
      const variance = this.calculateVariance(laplacianResult);

      console.log(`BlurDetectionService: Laplacian variance = ${variance}`);

      return {
        laplacianVariance: variance,
        isBlurry: variance < blurThreshold,
      };
    } catch (error) {
      console.error('BlurDetectionService: Error calculating Laplacian variance:', error);
      return {
        laplacianVariance: 0,
        isBlurry: true,
        error: `Error calculating Laplacian variance: ${error}`,
      };
    }
  }

  /**
   * Test method to verify blur detection service is working
   */
  async testBlurDetection(): Promise<BlurTestResult> {
    try {
      console.log('BlurDetectionService: Testing pure JavaScript implementation...');

      // Create a simple test image (10x10 checkerboard pattern)
      const testImageData = this.createTestImage(10, 10);

      // Test the blur detection
      const result = await this.calculateLaplacianVariance(testImageData);

      if (result.error) {
        return {
          success: false,
          error: result.error,
        };
      }

      return {
        success: true,
        message: `Pure JavaScript blur detection working! Variance: ${result.laplacianVariance.toFixed(2)}, Is Blurry: ${result.isBlurry}`,
      };
    } catch (error) {
      console.error('BlurDetectionService: Test failed:', error);
      return {
        success: false,
        error: `Test failed: ${error}`,
      };
    }
  }

  /**
   * Create a test image for testing purposes
   */
  private createTestImage(width: number, height: number): ImageData {
    const data = new Uint8ClampedArray(width * height * 4);

    // Create a checkerboard pattern (high contrast = low blur)
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * 4;
        const isWhite = (Math.floor(x / 2) + Math.floor(y / 2)) % 2 === 0;

        if (isWhite) {
          data[index] = 255;     // R
          data[index + 1] = 255; // G
          data[index + 2] = 255; // B
          data[index + 3] = 255; // A
        } else {
          data[index] = 0;       // R
          data[index + 1] = 0;   // G
          data[index + 2] = 0;   // B
          data[index + 3] = 255; // A
        }
      }
    }

    return {
      data,
      width,
      height,
    } as ImageData;
  }

  /**
   * Check if blur detection service is ready
   */
  isReady(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const BlurDetectionService = new BlurDetectionServiceClass();
