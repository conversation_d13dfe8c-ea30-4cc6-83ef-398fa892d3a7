import React, { useState, useEffect } from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import Slider from '~/components/inputs/Slider';
import StopTimer from '~/components/stoptimer/StopTimer';
import colors from '~/styles/colors';
import CheckboxGroup from '~/components/checkbox/CheckboxGroup';
import FilledButton from '~/components/buttons/FilledButton';
import { primarySolidButton } from '~/styles/buttons';
import ScreenWrapper from '~/screens/ScreenWrapper';
import { Divider } from '@rneui/base';
import { verticalSpace16 } from '~/styles/spacing';
import SearchableDropdownSelect from '~/components/select/dropdown/SearchableDropdownSelect';
import TextInput from '~/components/inputs/TextInput';
import { ArrowUpDown, Box, Email, EyeClosed } from '~/components/icons';
import Title from '~/components/text/Title';
import en from '~/localization/en';
import ToastMessage from '~/components/toast/ToastMessage';
import { CheckInCard } from '~/components/cards/CheckInCard';
import InfoItem from '~/components/info/InfoItem';
import LocationPinOutlined from '~/components/icons/LocationPinOutlined';
import Notes from '~/components/icons/Notes';
import MapView from '~/components/maps/MapView';
import ProofOfService from '~/components/ProofOfService';

const TestScreen = () => {
  const { bottom } = useSafeAreaInsets();
  const bottomTabBarHeight = useBottomTabBarHeight();

  const [allChecked, setAllChecked] = useState(false);
  const [selectedValue, setSelectedValue] = useState<string | null>(null);
  const [defaultInput, setDefaultInput] = useState('');
  const [secureInput, setSecureInput] = useState('');
  const [showBanner, setShowBanner] = useState(true);
  const [blurTestResult, setBlurTestResult] = useState<string>('');

  const [isTimerActive, setIsTimerActive] = useState(false);
  const [isButtonEnabled, setIsButtonEnabled] = useState(false);

  useEffect(() => {
    setIsTimerActive(true);
  }, []);

  const handleComplete = () => {
    setIsButtonEnabled(true);
  };

  const testBlurDetection = async () => {
    try {
      setBlurTestResult('Testing OpenCV.js...');

      // Dynamic import to avoid circular dependency issues
      const { runAllBlurDetectionTests } = await import('~/utils/testBlurDetection');
      const results = await runAllBlurDetectionTests();

      const initResult = results.initializationTest.success ? '✅' : '❌';
      const blurResult = results.blurDetectionTest.success ? '✅' : '❌';

      setBlurTestResult(
        `OpenCV Init: ${initResult} | Blur Detection: ${blurResult}\n` +
        `${results.initializationTest.message}\n` +
        `${results.blurDetectionTest.message}`
      );
    } catch (error) {
      setBlurTestResult(`Error: ${error}`);
    }
  };

  const scrollContainerStyle = {
    ...styles.scrollContainer,
    paddingBottom:
      bottomTabBarHeight +
      (bottom || styles.scrollContainer.padding) +
      styles.scrollContainer.padding +
      32,
  };

  return (
    <ScreenWrapper isKeyboardSensitive>
      <ScreenWrapper.Body>
        <ScrollView
          contentContainerStyle={scrollContainerStyle}
          showsVerticalScrollIndicator={false}>
          <View style={styles.div}>
            <FilledButton
              title="Test Blur Detection"
              testID="TestScreen.Button.BlurDetection"
              onClick={testBlurDetection}
              id="TestScreen.Button.BlurDetection"
              style={primarySolidButton}
              color="primary"
            />

            {blurTestResult && (
              <View style={styles.testResultContainer}>
                <Text style={styles.testResultText}>{blurTestResult}</Text>
              </View>
            )}

            <Divider style={verticalSpace16} />

            <Title title={'Test temperature'} icon={null} />
            <Title
              title={en.exchange}
              icon={<ArrowUpDown color={colors.darkBlue600} />}
            />
            <Title
              title={'Samples not out'}
              subtitle="Confirm you have knocked and share who you spoke to at the clinic"
              icon={null}
              showProgressText={true}
              progressText={'1/4'}
            />
            <Title
              title={'Parcel details'}
              icon={<Box color={colors.darkBlue600} />}
              textButtonVisible={true}
              textButtonTitle={'Add parcels'}
              onTextButtonPress={() => {}}
            />

            <MapView coordinate={[-112.0016533, 33.6401096]} />

            <Divider style={verticalSpace16} />

            <InfoItem
              title={'Getting there'}
              infoText={'4280 W Maple Ave, Seattle, WA 98101'}
              icon={<LocationPinOutlined />}
              enableCopy={true}
            />
            <InfoItem
              title={'Things to know'}
              infoText={
                'Lockbox is in the alley. Open until 7pm if no answer knock on door.'
              }
              icon={<Notes />}
            />

            <Divider style={verticalSpace16} />
          </View>
          {showBanner && (
            <ToastMessage
              variant="banner"
              type="warning"
              message="Current stops need to be finished before this one."
              linkText="Go to current stop"
              onPress={() => setShowBanner(false)}
              testID="ToastMessage.Banner"
            />
          )}

          <Divider style={verticalSpace16} />

          <FilledButton
            title="Show Banner"
            testID="TestScreen.Button.ShowBanner"
            onClick={() => setShowBanner(true)}
            id="TestScreen.Button.ShowBanner"
            style={primarySolidButton}
            color="primary"
          />

          <Divider style={verticalSpace16} />

          <CheckInCard
            title="Survey - Check In"
            description="Complete before timer ends"
            durationInMinutes={1}
            isTimerActive={isTimerActive}
            startTimestamp={Date.now()}
            isButtonEnabled={isButtonEnabled}
            onPress={() => console.log('Check-In Completed')}
            onComplete={handleComplete}
          />

          <Divider style={verticalSpace16} />

          <Slider
            id={'Slider'}
            min={50}
            max={104}
            step={6}
            defaultValue={77}
            title={'Lockbox temperature'}
            suffix={'°F'}
          />

          <Divider style={verticalSpace16} />

          <StopTimer
            durationInMinutes={1}
            strokeColor={colors.darkBlue600}
            id={'StopTimer'}
          />

          <Divider style={verticalSpace16} />

          <CheckboxGroup
            options={[
              {
                label: 'At least 10 frozen ice packs',
                value: 'CheckboxGroup.Checkbox.IcePacks',
              },
              {
                label: 'Disposable insulated pads',
                value: 'CheckboxGroup.Checkbox.InsulatedPads',
              },
              {
                label: 'Insulated cooler',
                value: 'CheckboxGroup.Checkbox.Cooler',
              },
            ]}
            onAllChecked={setAllChecked}
          />

          <FilledButton
            isDisabled={!allChecked}
            id={'TestScreen.Button.Continue'}
            title={'Continue'}
            onClick={() => console.log('Next Step')}
            style={primarySolidButton}
            color={'primary'}
          />

          <Divider style={verticalSpace16} />

          <View>
            <SearchableDropdownSelect
              id={'SearchableDropdownSelect'}
              label={'Searchable Dropdown Select'}
              placeholderText={'Select an option'}
              items={[
                'Option 1',
                'Option 2',
                'Option 3',
                'Option 4',
                'Option 5',
                'Option 6',
                'Option 7',
              ]}
              onValueSelection={setSelectedValue}
              selectedValue={selectedValue}
              searchPlaceHolderText={''}
            />
          </View>

          <Divider style={verticalSpace16} />

          <TextInput
            id={'TestScreen.InputField.email'}
            label="Email"
            placeholder="Email address"
            value={defaultInput}
            onChangeText={setDefaultInput}
            errorMessage="Email cannot be empty"
            icon={<Email />}
          />
          <TextInput
            id={'TestScreen.InputField.password'}
            label="Password"
            placeholder="Password"
            value={secureInput}
            onChangeText={setSecureInput}
            secureTextEntry={true}
            icon={<EyeClosed />}
            hint="Minimum 8 characters"
          />

          <Divider style={verticalSpace16} />

          <ProofOfService
            onImgCapture={() => {}}
            onImgSelect={() => {}}
            onCommentChange={() => {}}
            commentValue={''}
            onProofOfServiceChange={() => {}}
            proofOfServiceValue={''}
          />
        </ScrollView>
      </ScreenWrapper.Body>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    marginTop: 32,
    padding: 16,
    width: '100%',
  },
  div: {
    paddingTop: 16,
    gap: 16,
  },
  testResultContainer: {
    backgroundColor: colors.backgroundLight,
    padding: 12,
    borderRadius: 8,
    marginVertical: 8,
  },
  testResultText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: colors.grey700,
  },
});

export default TestScreen;
